package com.example.dop;

public class Practice {
    public static void main(String[] args) {

        Thread t = new Thread(() -> {
            System.out.println(Thread.currentThread().getState());
            System.out.println("Hello World");
            System.out.println(Thread.currentThread().getState());
        });
        System.out.println(t.getState());
        t.start();
        System.out.println(t.getState());

    }
}
